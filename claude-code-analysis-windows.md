# Augment Agent 与 Claude Code 协作工作流程指南 (Windows 10 版本)

## 概述
本文档是专为Windows 10环境优化的Augment Agent与Claude Code协作指南。使用Git Bash作为主要交互环境，确保最佳的兼容性和用户体验。

### 核心协作原则
- **角色分工明确**：Augment Agent = 项目经理，Claude Code = 开发者
- **委托式工作**：Augment Agent 绝不直接编写代码，所有编码工作委托给 Claude Code
- **智能时序控制**：使用10分钟倒计时机制避免频繁进程读取
- **持续性保证**：确保项目完整交付，不轻易中断对话

## Windows 10 环境配置

### 1. Git Bash 环境设置
**Git Bash 路径**: `C:\Program Files\git\bin\bash.exe`

### 2. 环境变量配置 (Git Bash)
```bash
# 在Git Bash中设置环境变量
export ANTHROPIC_AUTH_TOKEN="sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL"
export ANTHROPIC_BASE_URL="https://anyrouter.top"

# 验证环境变量设置
echo "Token: $ANTHROPIC_AUTH_TOKEN"
echo "Base URL: $ANTHROPIC_BASE_URL"
```

### 3. 永久环境变量设置
创建或编辑 `~/.bashrc` 文件：
```bash
# 编辑bashrc文件
nano ~/.bashrc

# 添加以下内容到文件末尾
export ANTHROPIC_AUTH_TOKEN="sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL"
export ANTHROPIC_BASE_URL="https://anyrouter.top"

# 重新加载配置
source ~/.bashrc
```

### 4. Claude Code 启动
```bash
# 在Git Bash中启动Claude Code
claude --dangerously-skip-permissions
```

## Windows 特定配置

### PowerShell 备用方案
如果需要在PowerShell中设置环境变量：
```powershell
# PowerShell环境变量设置
$env:ANTHROPIC_AUTH_TOKEN="sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL"
$env:ANTHROPIC_BASE_URL="https://anyrouter.top"

# 启动Claude Code
claude --dangerously-skip-permissions
```

### CMD 备用方案
```cmd
REM CMD环境变量设置
set ANTHROPIC_AUTH_TOKEN=sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL
set ANTHROPIC_BASE_URL=https://anyrouter.top

REM 启动Claude Code
claude --dangerously-skip-permissions
```

## Windows 工作流程脚本

### 快速启动脚本 (start-claude.sh)
```bash
#!/bin/bash
# Windows 10 Claude Code 快速启动脚本

echo "=== Windows 10 Claude Code 启动器 ==="
echo "Git Bash 环境: $(bash --version | head -1)"
echo "当前目录: $(pwd)"

# 检查环境变量
if [ -z "$ANTHROPIC_AUTH_TOKEN" ]; then
    echo "设置环境变量..."
    export ANTHROPIC_AUTH_TOKEN="sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL"
    export ANTHROPIC_BASE_URL="https://anyrouter.top"
fi

echo "环境变量已设置:"
echo "  Token: ${ANTHROPIC_AUTH_TOKEN:0:10}..."
echo "  Base URL: $ANTHROPIC_BASE_URL"

# 检查网络连接
echo "检查网络连接..."
if curl -s --connect-timeout 5 "$ANTHROPIC_BASE_URL" > /dev/null; then
    echo "✅ 网络连接正常"
else
    echo "⚠️ 网络连接可能有问题，请检查代理设置"
fi

# 启动Claude Code
echo "启动 Claude Code..."
claude --dangerously-skip-permissions
```

### 项目管理脚本 (project-manager.sh)
```bash
#!/bin/bash
# Windows 10 项目管理脚本

# 全局变量
PROJECT_DIR="$(pwd)"
PROGRESS_FILE=""
COUNTDOWN_COUNTER=0
DOCUMENT_REFRESH_INTERVAL=3

# 创建进度跟踪文档
create_progress_document() {
    local project_name="$1"
    local timestamp=$(date +%Y%m%d-%H%M%S)
    PROGRESS_FILE="project-progress-${timestamp}.md"
    
    cat > "$PROGRESS_FILE" << EOF
# 项目进度跟踪 - $project_name (Windows 10)

## 项目信息
- **项目名称**: $project_name
- **开始时间**: $(date '+%Y-%m-%d %H:%M:%S')
- **操作系统**: Windows 10
- **Shell环境**: Git Bash
- **负责人**: Augment Agent (项目经理)
- **执行者**: Claude Code (开发者)

## 当前状态
- **阶段**: 初始化
- **进度**: 0%
- **状态**: 准备中

## 任务列表
### 待完成任务
- [ ] 需求分析
- [ ] 任务分解
- [ ] 开发计划制定

### 进行中任务
- 无

### 已完成任务
- 无

## 执行日志
| 时间 | 操作 | 状态 | 备注 |
|------|------|------|------|
| $(date '+%H:%M:%S') | 项目初始化 | 开始 | 创建进度跟踪文档 |

## Windows 特定信息
- **Git Bash路径**: C:\\Program Files\\git\\bin\\bash.exe
- **工作目录**: $PROJECT_DIR
- **环境变量**: 已配置

---
*最后更新: $(date '+%Y-%m-%d %H:%M:%S')*
EOF

    echo "进度跟踪文档已创建: $PROGRESS_FILE"
    echo "$PROGRESS_FILE" > .current_progress_file
}

# Windows兼容的倒计时函数
standard_countdown_wait() {
    local wait_minutes=10
    local wait_seconds=$((wait_minutes * 60))
    
    echo "开始 ${wait_minutes} 分钟倒计时等待..."
    echo "这样可以避免 Augment 频繁读取进程"
    echo "倒计时开始时间: $(date '+%H:%M:%S')"
    
    # 使用Windows兼容的sleep命令
    sleep ${wait_seconds}
    
    echo "倒计时完成时间: $(date '+%H:%M:%S')"
    echo "现在可以安全地读取 Claude Code 的输出"
}

# 更新进度日志
update_progress_log() {
    local action="$1"
    local status="${2:-进行中}"
    local notes="${3:-}"
    
    if [ ! -f "$PROGRESS_FILE" ]; then
        echo "警告：进度文件不存在，创建新文件"
        create_progress_document "当前项目"
    fi
    
    local timestamp=$(date '+%H:%M:%S')
    local log_entry="| $timestamp | $action | $status | $notes |"
    
    # Windows兼容的文件操作
    echo "$log_entry" >> "$PROGRESS_FILE"
    
    # 更新最后更新时间
    sed -i "s/\*最后更新:.*\*/\*最后更新: $(date '+%Y-%m-%d %H:%M:%S')\*/" "$PROGRESS_FILE"
    
    echo "进度已更新: $action -> $status"
}

# 检查Claude Code连接
test_claude_connection() {
    echo "测试 Claude Code 连接..."
    
    # 检查进程是否运行
    if pgrep -f "claude" > /dev/null; then
        echo "✅ Claude Code 进程正在运行"
        return 0
    else
        echo "❌ Claude Code 进程未找到"
        return 1
    fi
}

# Windows环境检查
check_windows_environment() {
    echo "=== Windows 环境检查 ==="
    
    # 检查Git Bash
    if [ -n "$BASH_VERSION" ]; then
        echo "✅ Git Bash 环境: $BASH_VERSION"
    else
        echo "❌ 不在Git Bash环境中"
    fi
    
    # 检查环境变量
    if [ -n "$ANTHROPIC_AUTH_TOKEN" ]; then
        echo "✅ ANTHROPIC_AUTH_TOKEN 已设置"
    else
        echo "❌ ANTHROPIC_AUTH_TOKEN 未设置"
    fi
    
    if [ -n "$ANTHROPIC_BASE_URL" ]; then
        echo "✅ ANTHROPIC_BASE_URL: $ANTHROPIC_BASE_URL"
    else
        echo "❌ ANTHROPIC_BASE_URL 未设置"
    fi
    
    # 检查网络连接
    if command -v curl > /dev/null; then
        if curl -s --connect-timeout 5 "$ANTHROPIC_BASE_URL" > /dev/null 2>&1; then
            echo "✅ 网络连接正常"
        else
            echo "⚠️ 网络连接可能有问题"
        fi
    else
        echo "⚠️ curl 命令不可用，无法测试网络"
    fi
    
    echo "========================="
}

# 主函数
main() {
    echo "Windows 10 Claude Code 项目管理器启动"
    check_windows_environment
    
    if [ $# -eq 0 ]; then
        echo "用法: $0 <项目名称>"
        exit 1
    fi
    
    local project_name="$1"
    create_progress_document "$project_name"
    
    echo "项目管理器已准备就绪"
    echo "进度文件: $PROGRESS_FILE"
}

# 如果直接运行脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
```

## Windows 故障排除指南

### 常见问题及解决方案

#### 1. 环境变量未生效
```bash
# 检查当前环境变量
env | grep ANTHROPIC

# 重新设置环境变量
export ANTHROPIC_AUTH_TOKEN="sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL"
export ANTHROPIC_BASE_URL="https://anyrouter.top"
```

#### 2. 网络连接问题
```bash
# 测试网络连接
curl -v https://anyrouter.top

# 检查代理设置
echo $HTTP_PROXY
echo $HTTPS_PROXY
```

#### 3. Claude Code 无法启动
```bash
# 检查Claude Code是否安装
which claude

# 检查版本
claude --version

# 重新安装（如果需要）
npm install -g @anthropic-ai/claude-cli
```

#### 4. 权限问题
```bash
# 以管理员身份运行Git Bash
# 或者检查文件权限
ls -la claude
```

## 使用说明

### 1. 首次设置
```bash
# 1. 打开Git Bash
# 2. 导航到项目目录
cd /d/Xhchen/Ai/claudecodeDemo01

# 3. 设置环境变量
source start-claude.sh

# 4. 创建项目
./project-manager.sh "我的项目"
```

### 2. 日常使用
```bash
# 启动Claude Code
claude --dangerously-skip-permissions

# 在Claude Code中使用命令
/status
/help
```

### 3. 重要提醒
- ⚠️ **记住按回车键**：在Claude Code中输入命令后必须按回车键
- 🕐 **等待完成**：发送任务后等待Claude Code完成再继续
- 📝 **保持记录**：使用进度跟踪文档记录所有操作

## Windows 批处理脚本

### 快速启动批处理文件 (start-claude.bat)
```batch
@echo off
echo === Windows 10 Claude Code 启动器 ===
echo.

REM 设置环境变量
set ANTHROPIC_AUTH_TOKEN=sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL
set ANTHROPIC_BASE_URL=https://anyrouter.top

echo 环境变量已设置
echo Token: %ANTHROPIC_AUTH_TOKEN:~0,10%...
echo Base URL: %ANTHROPIC_BASE_URL%
echo.

REM 检查Claude Code是否安装
where claude >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: Claude Code 未安装或不在PATH中
    pause
    exit /b 1
)

echo 启动 Claude Code...
claude --dangerously-skip-permissions

pause
```

### PowerShell 启动脚本 (start-claude.ps1)
```powershell
# Windows 10 Claude Code PowerShell 启动脚本

Write-Host "=== Windows 10 Claude Code 启动器 ===" -ForegroundColor Green
Write-Host ""

# 设置环境变量
$env:ANTHROPIC_AUTH_TOKEN = "sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL"
$env:ANTHROPIC_BASE_URL = "https://anyrouter.top"

Write-Host "环境变量已设置:" -ForegroundColor Yellow
Write-Host "  Token: $($env:ANTHROPIC_AUTH_TOKEN.Substring(0,10))..."
Write-Host "  Base URL: $env:ANTHROPIC_BASE_URL"
Write-Host ""

# 检查Claude Code是否安装
try {
    $claudeVersion = claude --version 2>$null
    Write-Host "✅ Claude Code 已安装: $claudeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Claude Code 未安装或不在PATH中" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查网络连接
try {
    $response = Invoke-WebRequest -Uri $env:ANTHROPIC_BASE_URL -TimeoutSec 5 -UseBasicParsing
    Write-Host "✅ 网络连接正常" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 网络连接可能有问题，请检查代理设置" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "启动 Claude Code..." -ForegroundColor Cyan
claude --dangerously-skip-permissions
```

## Windows 集成开发环境

### VS Code 集成配置
创建 `.vscode/tasks.json` 文件：
```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "启动 Claude Code",
            "type": "shell",
            "command": "claude",
            "args": ["--dangerously-skip-permissions"],
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
            },
            "options": {
                "env": {
                    "ANTHROPIC_AUTH_TOKEN": "sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL",
                    "ANTHROPIC_BASE_URL": "https://anyrouter.top"
                },
                "shell": {
                    "executable": "C:\\Program Files\\git\\bin\\bash.exe",
                    "args": ["-c"]
                }
            }
        },
        {
            "label": "检查环境",
            "type": "shell",
            "command": "bash",
            "args": ["-c", "echo 'Git Bash: '$BASH_VERSION && echo 'Token: '${ANTHROPIC_AUTH_TOKEN:0:10}'...' && echo 'URL: '$ANTHROPIC_BASE_URL"],
            "group": "test",
            "options": {
                "env": {
                    "ANTHROPIC_AUTH_TOKEN": "sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL",
                    "ANTHROPIC_BASE_URL": "https://anyrouter.top"
                },
                "shell": {
                    "executable": "C:\\Program Files\\git\\bin\\bash.exe"
                }
            }
        }
    ]
}
```

### Windows Terminal 配置
在 Windows Terminal 的 `settings.json` 中添加：
```json
{
    "profiles": {
        "list": [
            {
                "name": "Claude Code (Git Bash)",
                "commandline": "C:\\Program Files\\git\\bin\\bash.exe",
                "startingDirectory": "D:\\Xhchen\\Ai\\claudecodeDemo01",
                "icon": "C:\\Program Files\\git\\mingw64\\share\\git\\git-for-windows.ico",
                "environment": {
                    "ANTHROPIC_AUTH_TOKEN": "sk-ADYsYcNb3i6p3y3TJ9ZSd6zanb2B17GOmNTJXGAU5P3qPYuL",
                    "ANTHROPIC_BASE_URL": "https://anyrouter.top"
                }
            }
        ]
    }
}
```

## 高级 Windows 工作流程

### 自动化项目管理脚本 (windows-project-manager.sh)
```bash
#!/bin/bash
# Windows 10 高级项目管理脚本

# 配置
CLAUDE_TIMEOUT=600  # 10分钟
MAX_RETRIES=3
LOG_DIR="./logs"
BACKUP_DIR="./backups"

# 创建必要目录
setup_directories() {
    mkdir -p "$LOG_DIR" "$BACKUP_DIR"
    echo "目录结构已创建"
}

# Windows 特定的进程管理
manage_claude_process() {
    local action="$1"

    case "$action" in
        "start")
            echo "启动 Claude Code..."
            # 在后台启动Claude Code并记录PID
            claude --dangerously-skip-permissions &
            echo $! > .claude_pid
            echo "Claude Code 已启动，PID: $(cat .claude_pid)"
            ;;
        "stop")
            if [ -f .claude_pid ]; then
                local pid=$(cat .claude_pid)
                echo "停止 Claude Code (PID: $pid)..."
                kill $pid 2>/dev/null || echo "进程可能已经停止"
                rm -f .claude_pid
            fi
            ;;
        "restart")
            manage_claude_process "stop"
            sleep 3
            manage_claude_process "start"
            ;;
        "status")
            if [ -f .claude_pid ]; then
                local pid=$(cat .claude_pid)
                if ps -p $pid > /dev/null 2>&1; then
                    echo "✅ Claude Code 正在运行 (PID: $pid)"
                else
                    echo "❌ Claude Code 进程不存在"
                    rm -f .claude_pid
                fi
            else
                echo "❌ 没有找到 Claude Code 进程记录"
            fi
            ;;
    esac
}

# Windows 兼容的日志记录
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local log_file="$LOG_DIR/claude-$(date '+%Y%m%d').log"

    echo "[$timestamp] [$level] $message" | tee -a "$log_file"
}

# 智能任务委托（Windows优化版）
delegate_task_windows() {
    local task="$1"
    local priority="${2:-normal}"
    local timeout="${3:-$CLAUDE_TIMEOUT}"

    log_message "INFO" "开始任务: $task"

    # 检查Claude Code状态
    manage_claude_process "status"

    # 发送任务
    echo "$task" > .current_task
    log_message "INFO" "任务已发送，开始等待..."

    # Windows兼容的等待机制
    local start_time=$(date +%s)
    local elapsed=0

    while [ $elapsed -lt $timeout ]; do
        sleep 30  # 每30秒检查一次
        elapsed=$(( $(date +%s) - start_time ))

        log_message "DEBUG" "等待中... ($elapsed/$timeout 秒)"

        # 检查是否有输出
        if [ -f .claude_output ]; then
            log_message "INFO" "检测到 Claude 输出"
            break
        fi
    done

    if [ $elapsed -ge $timeout ]; then
        log_message "WARN" "任务超时: $task"
        return 1
    fi

    log_message "INFO" "任务完成: $task"
    return 0
}

# 备份和恢复
backup_progress() {
    local backup_name="backup-$(date '+%Y%m%d-%H%M%S')"
    local backup_path="$BACKUP_DIR/$backup_name"

    mkdir -p "$backup_path"

    # 备份进度文件
    cp project-progress-*.md "$backup_path/" 2>/dev/null || true
    cp .current_progress_file "$backup_path/" 2>/dev/null || true
    cp .current_task "$backup_path/" 2>/dev/null || true

    log_message "INFO" "进度已备份到: $backup_path"
    echo "$backup_path" > .last_backup
}

# 主菜单
show_menu() {
    echo ""
    echo "=== Windows 10 Claude Code 项目管理器 ==="
    echo "1. 启动 Claude Code"
    echo "2. 停止 Claude Code"
    echo "3. 重启 Claude Code"
    echo "4. 检查状态"
    echo "5. 委托任务"
    echo "6. 查看日志"
    echo "7. 备份进度"
    echo "8. 环境检查"
    echo "9. 退出"
    echo "========================================="
    read -p "请选择操作 (1-9): " choice

    case $choice in
        1) manage_claude_process "start" ;;
        2) manage_claude_process "stop" ;;
        3) manage_claude_process "restart" ;;
        4) manage_claude_process "status" ;;
        5)
            read -p "请输入任务描述: " task
            delegate_task_windows "$task"
            ;;
        6)
            if [ -f "$LOG_DIR/claude-$(date '+%Y%m%d').log" ]; then
                tail -20 "$LOG_DIR/claude-$(date '+%Y%m%d').log"
            else
                echo "没有找到今天的日志文件"
            fi
            ;;
        7) backup_progress ;;
        8) check_windows_environment ;;
        9)
            manage_claude_process "stop"
            echo "再见！"
            exit 0
            ;;
        *) echo "无效选择，请重试" ;;
    esac
}

# 主循环
main_loop() {
    setup_directories
    log_message "INFO" "Windows 项目管理器启动"

    while true; do
        show_menu
        echo ""
        read -p "按回车键继续..."
    done
}

# 如果直接运行
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main_loop
fi
```

这个Windows版本充分利用了Git Bash的Unix兼容性，同时提供了Windows特定的配置和故障排除方案。包含了批处理脚本、PowerShell脚本、VS Code集成和高级项目管理功能。
